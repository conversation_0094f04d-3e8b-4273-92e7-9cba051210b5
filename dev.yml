version: '3.8'

services:
  ugs-ctp4d:
    image: harbor.unionstrongtech.com/ugs/ctp4d:0.1.1
    container_name: ugs-ctp4d
    working_dir: /app
    volumes:
      - /data/ctpdata:/data/ctpdata
      # 添加X11套接字映射以支持GPU渲染
      - /tmp/.X11-unix:/tmp/.X11-unix:rw
    ports:
      - "8085:8085"
    command: python examples/CTP4D.py --server --host 0.0.0.0 --port 8085
    environment:
      - PYTHONPATH=/app
      - PYTHONUNBUFFERED=1
      - APP_HOST=0.0.0.0
      - APP_PORT=8085
      - DATA_PATH=/data/ctpdata
      # GPU渲染相关环境变量
      - NVIDIA_VISIBLE_DEVICES=all
      - NVIDIA_DRIVER_CAPABILITIES=graphics,utility,compute
      - DISPLAY=${DISPLAY:-:0}
      # OpenGL相关环境变量
      - LIBGL_ALWAYS_INDIRECT=0
      - LIBGL_ALWAYS_SOFTWARE=0
      - __GLX_VENDOR_LIBRARY_NAME=nvidia
    stdin_open: true
    tty: true
    restart: unless-stopped
    # 启用GPU运行时支持
    deploy:
      resources:
        reservations:
          devices:
            - driver: nvidia
              count: all
              capabilities: [gpu, graphics, utility, compute]
