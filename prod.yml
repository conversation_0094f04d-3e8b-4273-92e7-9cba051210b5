version: '3.8'

services:
  ugs-ctp4d:
    build:
      context: .
      dockerfile: Dockerfile
    container_name: ugs-ctp4d-prod
    working_dir: /app
    volumes:
      # 生产环境：只挂载数据目录，不挂载源码
      - /data/ctpdata:/data/ctpdata
    ports:
      # 端口映射：宿主机8080 -> 容器8080
      - "8080:8080"
    command: python examples/CTP4D.py --server --host 0.0.0.0 --port 8080
    environment:
      # Python环境变量
      - PYTHONPATH=/app
      - PYTHONUNBUFFERED=1
      # 应用配置
      - APP_HOST=0.0.0.0
      - APP_PORT=8080
      - DATA_PATH=/data/ctpdata
    restart: unless-stopped
    networks:
      - ctp4d-network

networks:
  ctp4d-network:
    driver: bridge
    name: ctp4d-prod-network
