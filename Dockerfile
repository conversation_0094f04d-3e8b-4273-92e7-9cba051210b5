FROM nvidia/opengl:1.2-glvnd-devel-ubuntu20.04

# 设置非交互式安装，避免tzdata等包的交互式配置
ENV DEBIAN_FRONTEND=noninteractive
ENV TZ=Asia/Shanghai


RUN apt-get update && \
    apt-get install -y \
        python3 \
        python3-pip \
        python3-dev \    
        libx11-6 \
        libxrender1 \
        libxtst6 \
        libgl1-mesa-glx \
        libglib2.0-0 \
        python3-tk \
        tk-dev \
        libegl1 \
        libosmesa6 \
        libosmesa6-dev \
        libgles2-mesa-dev \
    || true && \
    rm -rf /var/lib/apt/lists/*

# 创建python软链接，使python命令可用
RUN ln -sf /usr/bin/python3 /usr/bin/python && \
    ln -sf /usr/bin/pip3 /usr/bin/pip    

WORKDIR /app

# 设置环境变量，限制线程数和禁用pip进度条
# 设置环境变量，限制线程数和禁用pip进度条
ENV OPENBLAS_NUM_THREADS=1 \
    NUMEXPR_NUM_THREADS=1 \
    MKL_NUM_THREADS=1 \
    OMP_NUM_THREADS=1 \
    VECLIB_MAXIMUM_THREADS=1 \
    NVIDIA_VISIBLE_DEVICES=all \
    PIP_DISABLE_PIP_VERSION_CHECK=1 \
    PIP_NO_CACHE_DIR=1 \
    PIP_PROGRESS_BAR=off \
    PIP_TIMEOUT=1000 \
    NVIDIA_VISIBLE_DEVICES=all \
    PIP_DISABLE_PIP_VERSION_CHECK=1 \
    PIP_NO_CACHE_DIR=1 \
    PIP_PROGRESS_BAR=off \
    PIP_TIMEOUT=1000

# 复制requirements文件
COPY requirements.txt ./

# 安装Python依赖（分步骤，避免线程问题）
RUN python -m pip install --no-cache-dir --disable-pip-version-check pip==23.3.2
RUN pip install --no-cache-dir --timeout=1000 --retries=3 numpy
RUN pip install --no-cache-dir --timeout=1000 --retries=3 -r requirements.txt

# 复制应用代码
COPY mipf/ ./mipf/
COPY examples/ ./examples/

# 设置Python环境变量
ENV PYTHONPATH=/app
ENV PYTHONUNBUFFERED=1

# 暴露端口
EXPOSE 8080

RUN apt-get update && apt-get upgrade -y && apt-get clean


