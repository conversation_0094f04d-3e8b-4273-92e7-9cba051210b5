## verion 1

```mermaid
graph TD
    ServerState --> Engine(engine.py);
    Engine --> DataStorage(data_storage);
    Engine --> RenderWindowManager(render_window_manager.py);
    Engine --> ServerController(server.controller);

    subgraph Data Management
        DataStorage
        Data(data.py)
        DataManager(data_manager.py)
    end

    subgraph Rendering
        RenderWindowManager
        RenderWindow(render_window.py)
    end

    subgraph Mapping
        MapperManager(mapper_mananger.py)
        Mapper(mapper.py)
    end

    subgraph Processing
      PipelineManager(pipeline_manager.py)
    end


    DataStorage -- contains --> Data;

    %% Connections based on engine.py imports/usage
    Engine -- uses --> Data;
    Engine -- uses --> RenderWindowManager;
    Engine -- uses --> MapperManager;
    Engine -- uses --> PipelineManager;
    Engine -- uses --> RenderWindow;
    Engine -- uses --> Mapper;
```

## version 2 

```mermaid
graph TD
    ServerState --> Engine(engine.py);
    Engine --> DataStorage(data_storage);
    Engine --> RenderWindowManager(render_window_manager.py);
    Engine --> ServerController(server.controller);

    subgraph Data Management
        DataStorage
        Data(data.py)
        DataManager(data_manager.py)
    end

    subgraph Rendering
        RenderWindowManager -- manages --> RenderWindow(render_window.py);
    end

    subgraph Mapping
        MapperManager(mapper_mananger.py)
        Mapper(mapper.py)
    end

    subgraph Processing
      PipelineManager(pipeline_manager.py)
    end


    DataStorage -- contains --> Data;

    %% Connections based on engine.py imports/usage
    Engine -- uses --> Data;
    Engine -- uses --> RenderWindowManager;
    Engine -- uses --> MapperManager;
    Engine -- uses --> PipelineManager;
    Engine -- uses --> RenderWindow; 
    
    %% Engine might still directly use RenderWindow enums/types
    Engine -- uses --> Mapper;

    %% Connections based on render_window_manager.py
    RenderWindowManager -- calls_update_on --> RenderWindow;

```

## version 3

```mermaid
graph TD
    ServerState --> Engine(engine.py);
    Engine --> DataStorage(data_storage);
    Engine --> RenderWindowManager(render_window_manager.py);
    Engine --> MapperManager(mapper_mananger.py);
    Engine --> ServerController(server.controller);

    subgraph Data Management
        DataStorage -- contains --> Node(Node in data.py);
        Data(data.py);
        DataManager(data_manager.py);
    end

    subgraph Rendering
        RenderWindowManager -- manages --> RenderWindow(render_window.py);
    end

    subgraph Mapping
        MapperManager -- manages --> Mapper(mapper.py);
        Mapper -- operates_on --> Node;
    end

    subgraph Processing
      PipelineManager(pipeline_manager.py);
    end

    %% Node definition likely in data.py
    Data -- defines --> Node;

    %% Connections based on engine.py imports/usage
    Engine -- uses --> Data;
    Engine -- uses --> RenderWindowManager;
    Engine -- uses --> MapperManager;
    Engine -- uses --> PipelineManager;
    Engine -- uses --> RenderWindow;
    Engine -- uses --> Mapper;

    %% Connections based on render_window_manager.py
    RenderWindowManager -- calls_update_on --> RenderWindow;

    %% Connections based on mapper_mananger.py
    MapperManager -- associates --> Mapper;
    MapperManager -- associates_with --> Node;
```

## version 4

```mermaid
graph TD
    ServerState --> Engine(engine.py);
    ServerState --> PipelineManager(pipeline_manager.py); 
    
    %% PipelineManager writes to state
    Engine --> DataStorage(data_storage);
    Engine --> RenderWindowManager(render_window_manager.py);
    Engine --> MapperManager(mapper_mananger.py);
    Engine --> PipelineManager; 
    
    %% Engine likely uses PipelineManager
    Engine --> ServerController(server.controller);

    subgraph Data Management
        DataStorage -- contains --> Node(Node in data.py);
        DataStorage -- read_by --> PipelineManager;
        DataStorage -- read_by --> Engine; 
        
        %% Engine accesses nodes via data_storage
        Data(data.py);
        DataManager(data_manager.py);
        PipelineManager -- modifies --> Node;
    end

    subgraph Rendering
        RenderWindowManager -- manages --> RenderWindow(render_window.py);
    end

    subgraph Mapping
        MapperManager -- manages --> Mapper(mapper.py);
        Mapper -- operates_on --> Node;
    end

    subgraph Processing
      PipelineManager;
    end

    %% Node definition likely in data.py
    Data -- defines --> Node;
    Data -- defines --> DataStorage; 
    
    %% DataStorage structure likely defined here

    %% Connections based on engine.py imports/usage
    Engine -- uses --> Data;
    Engine -- uses --> RenderWindowManager;
    Engine -- uses --> MapperManager;
    Engine -- uses --> PipelineManager;
    Engine -- uses --> RenderWindow;
    Engine -- uses --> Mapper;

    %% Connections based on render_window_manager.py
    RenderWindowManager -- calls_update_on --> RenderWindow;

    %% Connections based on mapper_mananger.py
    MapperManager -- associates --> Mapper;
    MapperManager -- associates_with --> Node;
```

## version 5

```mermaid
graph TD
    subgraph Application Logic
        ServerState
        ServerController
        Engine(engine.py)
    end

    subgraph Data Management
        direction LR
        DataStorage(DataStorage in data.py) -- contains --> Node(DataNode in data.py);
        Node -- holds_ref_via_manager --> BaseData(BaseData/ImageData/etc. in data.py);
        DataManager(data_manager.py) -- manages --> BaseData;
        Node -- uses --> DataManager; 
        
        %% Node uses data_manager to get/set BaseData
        PipelineManager(pipeline_manager.py) -- reads/modifies --> DataStorage;
        PipelineManager -- reads/modifies --> Node;
        DataStorage -- triggers --> Callbacks;
        PipelineManager -- writes_to --> ServerState;
    end

    subgraph Rendering
        direction LR
        RenderWindowManager(render_window_manager.py) -- manages --> RenderWindow(render_window.py);
        RenderWindow -- displays --> Node; 
        
        %% Assumed connection
    end

    subgraph Mapping
        direction LR
        MapperManager(mapper_mananger.py) -- associates --> Mapper(mapper.py);
        MapperManager -- associates --> Node;
        Mapper -- operates_on --> Node;
        Mapper -- generates_representation_for --> RenderWindow; 
        
        %% Assumed connection
    end


    %% Core Interactions
    Engine -- interacts_with --> ServerState;
    Engine -- interacts_with --> ServerController;
    Engine -- interacts_with --> DataStorage;
    Engine -- interacts_with --> RenderWindowManager;
    Engine -- interacts_with --> MapperManager;
    Engine -- interacts_with --> PipelineManager;

    %% Render Update Flow
    Engine -- triggers_update --> RenderWindowManager;
    RenderWindowManager -- calls_update_on --> RenderWindow;

    %% Callback Connection (Example)
    Callbacks -- notify --> PipelineManager; 
    
    %% PipelineManager might listen for DataStorage events
```

## version 6

```mermaid
graph TD
    subgraph Application Logic & Control
        ServerState
        ServerController
        Engine(engine.py)
    end

    subgraph Data Management
        direction LR
        DataStorage(DataStorage in data.py) -- contains --> Node(DataNode in data.py);
        Node -- uses --> DataManager(data_manager.py);
        %% Node gets/sets data via DataManager using its ID
        DataManager -- manages --> BaseData(BaseData/ImageData/etc. in data.py);
        %% DataManager holds actual data, keyed by Node ID
        PipelineManager(pipeline_manager.py) -- uses --> DataStorage;
        PipelineManager -- uses --> Node;
        DataStorage -- triggers --> Callbacks;
        PipelineManager -- writes_to --> ServerState;
    end

    subgraph Rendering
        direction LR
        RenderWindowManager(render_window_manager.py) -- manages --> RenderWindow(render_window.py);
        RenderWindow -- displays --> VisualRepresentation;
        %% RenderWindow shows the output
    end

    subgraph Mapping
        direction LR
        MapperManager(mapper_mananger.py) -- associates --> Mapper(mapper.py);
        MapperManager -- associates --> Node;
        Mapper -- operates_on --> Node;
        Mapper -- reads --> BaseData;
        %% Mapper likely needs the actual data via Node->DataManager->BaseData
        Mapper -- generates --> VisualRepresentation;
        %% Mapper creates something visualizable
    end


    %% Core Interactions & Flow
    Engine -- interacts_with --> ServerState;
    Engine -- interacts_with --> ServerController;
    Engine -- interacts_with --> DataStorage;
    Engine -- interacts_with --> RenderWindowManager;
    Engine -- interacts_with --> MapperManager;
    Engine -- interacts_with --> PipelineManager;
    Engine -- interacts_with --> Node;
    %% Engine modifies Node properties directly or via DataStorage

    %% Render Update Flow
    Engine -- triggers_update --> RenderWindowManager;
    RenderWindowManager -- calls_update_on --> RenderWindow;

    %% State Update Flow
    PipelineManager -- updates --> ServerState;
    %% Reflects DataStorage changes

    %% Data Access Flow Example for Mapper: Mapper utilizes Node, which uses DataManager to access BaseData.

    %% Node Management Flow
    ExternalAction -- creates/modifies --> Node;
    Node -- actions_trigger --> DataStorage;
    %% Add/Remove/Modify
    DataStorage -- triggers --> Callbacks;
    Callbacks -- notify --> PipelineManager;
    %% PipelineManager updates State
```

## 类关系图

```mermaid
---
config:
  theme: default
---
classDiagram
    class BaseData {
        <<Abstract>>
        +DataType type
        +String name
        #Geometry _geometry
        +get_geometry() Geometry
        +get_bounds() List
        +read_data(String filename) void
    }
    class ImageData {
        +vtkImageData _image
        +read_data(String filename) void
        +get_image() vtkImageData
    }
    class SurfaceData {
        +vtkPolyData _polydata
        +read_data(String filename) void
        +get_polydata() vtkPolyData
    }
    class PointSetData {
        +vtkPoints _pointset
        +read_data(String filename) void
        +get_pointset() vtkPoints
    }
    class Geometry {
    }
    class DataNode {
        +Dict properties
        +DataNode parent
        +set_data(BaseData data) void
        +get_data() BaseData
        +get(String key) Any
        +set(String key, Any value) void
    }
    class DataStorage {
        +Dict~UUID, DataNode~ nodes
        +Dict~str, Set~str~~ children_map
        +add_node(DataNode node, DataNode parent_node) void
        +remove_node(UUID id) void
        +get_node(UUID id) DataNode
        +register_callback(Callable callback, Event event_name) void
        +trigger_callbacks(Event event_name) void
    }
    class DataManager {
        #Dict~UUID, BaseData~ dataset
        +add_data(BaseData data, UUID uid) void
        +remove_data(UUID uid) void
        +get_data(UUID uid) BaseData
    }
    class Mapper {
        #DataNode node
        +set_node(DataNode node) void
    }
    class MapperManager {
        #Dict~UUID, Dict~MapperType, Mapper~~ mappers
        +set_mapper(DataNode node, Mapper mapper, MapperType type) void
        +get_mapper(DataNode node, MapperType type) Mapper
    }
    class RenderWindow {
        +ViewType view_type
        +update() void
        +set_depth_peeling(bool enabled) void
    }
    class RenderWindowManager {
        #List~RenderWindow~ render_windows
        +add_renderwindow(RenderWindow rw) void
        +get_activate_renderwindow() RenderWindow
        +request_update_all() void
    }
    class PipelineManager {
        #ServerState _state
        #String _name
        #DataStorage data_storage
        +update() List
        +add_node(DataNode node) UUID
        +remove_node(UUID id) void
        +toggle_visible(UUID id) bool
    }
    class Engine {
      <<Conceptual>>
      +initialize_internal_binding(Server server, DataStorage ds) void
    }
    class ServerState { <<External>> }
    class ServerController { <<External>> }
    class VisualRepresentation { <<Conceptual>> }
    ImageData --|> BaseData
    SurfaceData --|> BaseData
    PointSetData --|> BaseData
    BaseData "1" *-- "1" Geometry
    DataStorage "1" o-- "*" DataNode : contains
    DataManager "1" o-- "*" BaseData : manages_via_id
    RenderWindowManager "1" o-- "*" RenderWindow : manages
    MapperManager "1" *-- "*" Mapper : manages_via_id_type
    PipelineManager "1" *-- "1" DataStorage : uses
    PipelineManager "1" *-- "1" ServerState : uses
    DataNode "1" ..> DataManager : uses_for_data_access
    DataNode "1" ..> BaseData : holds_conceptual_ref
    PipelineManager ..> DataNode : modifies_properties
    PipelineManager ..> ServerState : updates
    MapperManager ..> DataNode : associates_with
    Mapper ..> DataNode : operates_on
    Mapper ..> BaseData : reads_data_via_node
    Mapper ..> VisualRepresentation : generates
    RenderWindow ..> VisualRepresentation : displays
    RenderWindowManager ..> RenderWindow : calls_update
    Engine ..> ServerState : reads
    Engine ..> ServerController : uses
    Engine ..> DataStorage : reads
    Engine ..> DataNode : modifies_properties
    Engine ..> RenderWindowManager : triggers_update
    Engine ..> MapperManager : uses
    Engine ..> PipelineManager : uses %% Potentially for setup or coordination
    DataStorage ..> PipelineManager : notifies_via_callback %% PipelineManager likely registers callbacks
```