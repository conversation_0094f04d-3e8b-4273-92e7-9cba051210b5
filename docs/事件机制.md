## 1、数据节点属性变化的事件调用链

```mermaid
graph TD
    A[DataNode.setProperty方法] --触发--> B[DataNode.propertyChanged事件]
    B[DataNode.propertyChanged事件] --触发--> C[DataStorage.nodePropertyChanged事件]
    %% 通过DataStorage._setupNodeListeners %%
    C[DataStorage.nodePropertyChanged事件] --触发--> D[DataController._onNodePropertyChanged方法]
    D[DataController._onNodePropertyChanged方法] --触发--> E[DataController.nodePropertyChanged事件]

    %% Mapper中的监听 %%
    B[DataNode.propertyChanged事件] --触发--> F[Mapper._onDataNodePropertyChanged方法]
    F[Mapper._onDataNodePropertyChanged方法] --调用--> G[Mapper.update方法]
    F[Mapper._onDataNodePropertyChanged方法] --触发--> H[Mapper.dataNodePropertyChanged事件]
```

## 2、数据节点移除的事件调用链

```mermaid
graph TD
    A[DataController.removeNode方法] --调用--> B[DataStorage.removeNode方法]
    B[DataStorage.removeNode方法] --触发--> C[DataStorage.nodeRemoved事件]
    C[DataStorage.nodeRemoved事件] --触发--> D[DataController._onNodeRemoved方法]
    D[DataController._onNodeRemoved方法] --触发--> E[DataController.nodeRemoved事件]
    C[DataStorage.nodeRemoved事件] --触发--> F[RenderWindow._onNodeRemoved方法]
    F[RenderWindow._onNodeRemoved方法] --调用--> G[RenderWindow._removeNode方法]
    G[RenderWindow._removeNode方法] --调用--> H[RenderWindow.render方法]

    %% 特殊情况：当从父节点移除子节点时 %%
    I["DataNode.setParent(null)方法"] --调用--> J[DataNode._removeChild方法]
    J[DataNode._removeChild方法] --触发--> K[DataNode.childRemoved事件]
    K[DataNode.childRemoved事件] --触发--> C[DataStorage.nodeRemoved事件]
```

## 3、数据内容变化的事件调用链

```mermaid
graph TD
    A[DataNode.setData方法] --触发--> B[DataNode.dataChanged事件]
    B[DataNode.dataChanged事件] --触发--> C[DataStorage.nodeDataChanged事件]
    %% 通过DataStorage._setupNodeListeners %%
    C[DataStorage.nodeDataChanged事件] --触发--> D[DataController._onNodeDataChanged方法]
    D[DataController._onNodeDataChanged方法] --触发--> E[DataController.nodeDataChanged事件]

    %% Mapper中的监听 %%
    B[DataNode.dataChanged事件] --触发--> F[Mapper._onDataNodeDataChanged方法]
    F[Mapper._onDataNodeDataChanged方法] --调用--> G[Mapper.update方法]
    F[Mapper._onDataNodeDataChanged方法] --触发--> H[Mapper.dataNodeDataChanged事件]
```

## 4、渲染相关的事件调用链

```mermaid
graph TD
    A[RenderWindow.render方法] --调用--> B[VTK渲染窗口的render方法]

    %% 节点添加到渲染窗口 %%
    C[RenderWindow._addNode方法] --调用--> D[MapperFactory.createMapper方法]
    C[RenderWindow._addNode方法] --调用--> E[Mapper.generateDataForRenderer方法]
    C[RenderWindow._addNode方法] --调用--> A[RenderWindow.render方法]

    %% 窗口大小变化 %%
    F[window.resize事件] --触发--> G[RenderWindow._onWindowResize方法]
    G[RenderWindow._onWindowResize方法] --调用--> A[RenderWindow.render方法]

    %% 渲染窗口管理器 %%
    H[RenderWindowManager.renderAll方法] --调用--> I[所有RenderWindow的render方法]
```

## 5、活动节点变化的事件调用链

```mermaid
graph TD
    A[DataController.setActiveNode方法] --触发--> B[DataController.activeNodeChanged事件]
```

## 6、数据清除的事件调用链

```mermaid
graph TD
    A[DataController.clearData方法] --调用--> B[DataStorage.removeNode方法]
    %% 对每个非根节点 %%
    A[DataController.clearData方法] --触发--> C[DataController.dataCleared事件]
```

## 7、渲染窗口初始化和销毁的事件调用链

```mermaid
graph TD
    A[RenderWindow.initialize方法] --触发--> B[RenderWindow.initialized事件]
    C[RenderWindow.destroy方法] --触发--> D[RenderWindow.destroyed事件]
```



## 8、Node添加调用链

```mermaid
graph TD
G[DataController.createXXXNode方法] --调用--> F
F[DataController.addNode方法] --调用--> A
A[DataStorage.addNode方法] --触发--> B[DataStorage.nodeAdded事件]
B --触发--> C[DataController._onNodeAdded方法]
B --触发--> E[RenderWindow._onNodeAdded方法]


DataNode.setParent方法 --调用--> DataNode._addChild方法 --触发--> H
H["根节点.childAdded事件"] --触发--> B
```

## 架构

根据代码库，medical-imaging-js 中的核心数据模型如下：

### 核心数据模型

1. **BaseData（抽象类）**：所有数据类型的基础类
2. **VolumeData**：表示 3D 体积数据（例如 CT/MRI 扫描）
3. **SurfaceData**：表示表面网格数据
4. **PointSetData**：表示点集数据（例如地标）

### 数据管理
* **DataNode**：封装数据及其属性，支持层级结构
* **DataStorage**：管理所有数据节点的中央存储库
* **DataType**：定义不同数据类型（UNDEFINED、VOLUME、SURFACE、POINT_SET）的枚举

### 关系

* **继承**：VolumeData、SurfaceData 和 PointSetData 均继承自 BaseData

* **组合**：
  * DataNode 包含一个 BaseData 对象
  * DataStorage 包含多个 DataNode 对象，这些对象采用层级结构
  * DataNode 可以具有父子关系（树形结构）

* **关联**：
  * BaseData 使用 DataType 来标识其类型
  * BaseData具有用于空间信息的几何对象

该架构遵循清晰的关注点分离，其中数据模型独立于渲染逻辑，从而允许灵活地进行数据可视化和操作。



## 渲染管线

### 图像数据3D（Image）

| 对象     | Gemini 2.5                         | Agument      | pymipf（Trame）                                       |
| -------- | ---------------------------------- | ------------ | ----------------------------------------------------- |
| DataType | vtk.Common.DataModel.vtkImageData  | 同Gemini 2.5 | 同Gemini 2.5                                          |
| Mapper   | vtk.Rendering.Core.vtkVolumeMapper | 同Gemini 2.5 | vtk.Rendering.Volume.OpenGL2.**vtkSmartVolumeMapper** |
| Actor    | vtk.Rendering.Core.vtkVolume       | 同Gemini 2.5 | 同Gemini 2.5                                          |

medical-imaging-js中使用vtk.Rendering.Core.vtkVolumeMapper和pymipf中使用vtkSmartVolumeMapper的区别。

### vtkVolumeMapper与vtkSmartVolumeMapper的区别

1. 基本区别：
   - vtkVolumeMapper：
     - 是VTK.js中的基础体积渲染映射器
     - 提供基本的体积渲染功能
     - 需要开发者手动配置渲染参数
     - 在medical-imaging-js中使用，因为它是VTK.js的标准组件
   - vtkSmartVolumeMapper：
     - 是VTK(C++)中的高级体积渲染映射器
     - 能够自动选择最适合当前硬件和数据的渲染技术
     - 在pymipf中使用，因为**它是VTK(C++)的组件**，通过Python绑定可用

### 图像数据（Image）——2D图像或3D体数据的某个切片

| 对象     | Gemini 2.5                                   | Agument | pymipf（Trame）                      |
| -------- | -------------------------------------------- | ------- | ------------------------------------ |
| DataType | vtk.Common.DataModel.vtkImageData            |         | 同Gemini 2.5                         |
| Mapper   | vtk.Rendering.Core.vtkImageMapper            |         | vtk.Imaging.Core.**vtkImageReslice** |
| Actor    | vtk.Rendering.Core.vtkImageSlice或vtkActor2D |         | vtk.Rendering.Core.**vtkImageActor** |

### 表面数据3D（Surface）

| 对象     | Gemini 2.5                       | Agument      | pymipf（Trame）                      |
| -------- | -------------------------------- | ------------ | ------------------------------------ |
| DataType | vtk.Common.DataModel.vtkPolyData | 同Gemini 2.5 | 同Gemini 2.5                         |
| Mapper   | vtk.Rendering.Core.vtkMapper     | 同Gemini 2.5 | vtk.Rendering.Core.vtkPolyDataMapper |
| Actor    | vtk.Rendering.Core.vtkActor      | 同Gemini 2.5 | 同Gemini 2.5                         |

为什么VTK.js中使用vtk.Rendering.Core.vtkMapper，而Trame中使用vtk.Rendering.Core.vtkPolyDataMapper？

- Trame (Python VTK) 使用 `vtkPolyDataMapper` 是因为这是 C++/Python VTK 的标准做法，其中 `vtkMapper` 是抽象的。
- VTK.js 中，虽然也存在 `vtkPolyDataMapper` 这个类，但更常见的是直接使用 `vtk.Rendering.Core.vtkMapper` 来渲染 `vtkPolyData`，因为 VTK.js 中的 `vtkMapper` 是一个可以直接处理 `vtkPolyData` 的具体类。
- **在 VTK.js 中，你可以安全地使用 `vtkMapper.newInstance()` 来处理 `vtkPolyData`。** **如果你愿意，也可以使用** `vtkPolyDataMapper.newInstance()`，这会更显式，但在许多常见情况下，两者的最终渲染效果和基本功能可能没有明显区别。VTK.js 的设计使得 `vtkMapper` 在处理 `vtkPolyData` 时扮演了主要角色。

这种差异主要是由于 VTK.js 在设计和实现上针对 Web 环境进行了一些调整和简化，同时尽量保持与传统 VTK 概念的兼容性。

### 点集数据3D（Points）

| 对象     | Gemini 2.5                                                   | Agument                   | pymipf（Trame）                          |
| -------- | ------------------------------------------------------------ | ------------------------- | ---------------------------------------- |
| DataType | vtk.Common.Core.vtkPoints——》vtk.Common.DataModel.vtkPolyData | vtk.Common.Core.vtkPoints | 同Agument                                |
| Mapper   | vtk.Rendering.Core.vtkMapper                                 | 同Gemini 2.5              | vtk.Rendering.Core.**vtkPolyDataMapper** |
| Actor    | vtk.Rendering.Core.vtkActor                                  | 同Gemini 2.5              | 同Gemini 2.5                             |
