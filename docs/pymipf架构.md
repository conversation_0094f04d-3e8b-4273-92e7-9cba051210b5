### 1. 数据驱动的状态同步（主要调用链）

```mermaid
sequenceDiagram
    participant User
    participant DataStorage
    participant PipelineManager
    participant ServerState
    participant RenderWindow
    participant Mapper
    participant VTKRenderer

    User->>DataStorage: 添加/修改数据
    Note over DataStorage: 触发 ADD_NODE 或 UPDATE_NODE 事件
    DataStorage->>PipelineManager: 通知变更（通过回调）
    PipelineManager->>ServerState: 更新状态（如 pipeline_name）
    Note over ServerState: 触发状态变更事件
    ServerState->>RenderWindow: 监听到状态变更
    RenderWindow->>Mapper: 获取对应节点的 Mapper
    RenderWindow->>Mapper: generate_data_for_renderer(renderer)
    Mapper->>VTKRenderer: 更新/添加 Actor
    VTKRenderer->>User: 渲染更新的场景
```

### 2. 初始化渲染（首次加载）

```mermaid
sequenceDiagram
    participant User
    participant AppBase
    participant DataStorage
    participant MapperManager
    participant Mapper
    participant RenderWindow
    participant VTKRenderer

    User->>AppBase: 加载数据（如 load() 方法）
    AppBase->>DataStorage: 创建并添加 DataNode
    DataStorage->>MapperManager: 为节点创建 Mapper
    RenderWindow->>MapperManager: 获取节点的 Mapper
    RenderWindow->>Mapper: generate_data_for_renderer(renderer)
    Mapper->>VTKRenderer: 创建并添加初始 Actor
    VTKRenderer->>User: 显示初始场景
```

### 3. 属性变更（如改变颜色、透明度）

```mermaid
sequenceDiagram
    participant User
    participant UI
    participant ServerState
    participant Engine
    participant DataNode
    participant Mapper
    participant VTKRenderer

    User->>UI: 修改属性（如拖动透明度滑块）
    UI->>ServerState: 更新状态值
    ServerState->>Engine: 触发状态变更回调
    Engine->>DataNode: 更新节点属性
    Note over DataNode: 属性更新触发渲染更新
    DataNode->>Mapper: 通知属性变更
    Mapper->>Mapper: generate_data_for_renderer(renderer)
    Mapper->>VTKRenderer: 更新 Actor 属性
    VTKRenderer->>User: 显示更新后的场景
```

### 4. 视图布局变更

```mermaid
sequenceDiagram
    participant User
    participant Workbench
    participant RenderWindow
    participant Mapper
    participant VTKRenderer

    User->>Workbench: 切换视图布局
    Workbench->>RenderWindow: 创建/重置渲染窗口
    RenderWindow->>Mapper: 获取需要显示的数据的 Mapper
    RenderWindow->>Mapper: generate_data_for_renderer(renderer)
    Mapper->>VTKRenderer: 设置适合新视图的 Actor
    VTKRenderer->>User: 在新布局中显示场景
```

### 5. 强制刷新场景

```mermaid
sequenceDiagram
    participant User
    participant AppBase
    participant RenderWindowManager
    participant RenderWindow
    participant Mapper
    participant VTKRenderer

    User->>AppBase: 触发刷新（如点击刷新按钮）
    AppBase->>RenderWindowManager: request_update_all()
    RenderWindowManager->>RenderWindow: 遍历所有窗口
    RenderWindow->>Mapper: generate_data_for_renderer(renderer)
    Mapper->>VTKRenderer: 重新生成/更新 Actor
    VTKRenderer->>User: 刷新显示
```


| 项目名称 | 主要开发语言 | 构建工具 | 依赖管理方式 | 模块系统 | 浏览器兼容性处理 |
|----------------------|--------------------------------|------------------------|-----------------------------------------------------------------|------------------------------------|---------------------------------------------------------------|
| itk-vtk-viewer | JavaScript（含 TypeScript 声明） | Webpack (+ Babel) | • npm 发布<br>• @kitware/vtk.js、itk-wasm 作为 peerDependencies<br>• package-lock.json 锁定版本 | ES Module（源码）+ UMD（打包产物） | • Babel 转 ES5<br>• 核心 polyfill（如 core-js、regenerator-runtime） |
| OHIF Viewer | TypeScript + JavaScript | Webpack | • Monorepo（Yarn Workspaces + Lerna）<br>• npm 安装、lockfile 锁定 | CommonJS (服务端/插件) + ES Module（前端） | • Babel 转译（支持 IE11+）<br>• 按需引入 polyfill |
| ParaView Glance | JavaScript | Webpack | • npm 安装、package-lock.json 锁定<br>• 通过 externals/alias 控制依赖 | ES Module（源码）+ UMD（打包产物） | • Babel 转 ES5<br>• polyfill |
| medical-imaging-js | TypeScript | Rollup | • npm + package-lock.json<br>• 在 Rollup 中声明 external/peerDependencies | ESNext Module（npm 发布）+ IIFE（浏览器版） | • tsconfig target=ES2020<br>• Rollup banner 模拟全局对象<br>• 用户侧可按需添加 polyfill