## MultiView.py调用流程

```mermaid
graph TD
    A["1 MultiView.py:main"] --> B["2 Workbench.__init__"]
    B --> C["3 Workbench.setupui"]
    C --> D["4 Workbench.load\n接收文件路径和名称\n根据文件类型调用不同的导入函数\n支持多种格式：nii/nii.gz/vti/mha/nrrd/vtp/stl"]
    D --> D1["4.1 判断文件类型\n根据文件扩展名选择导入方式"]
    D1 --> D2["4.1.1 调用相应的导入函数\nimport_image_file或import_surface_file"]
    D2 --> D3["******* import_image_file\n创建ImageData或SurfaceData对象\n调用read_data方法读取文件内容\n创建DataNode并设置数据"]
    D3 --> D31["*******.1 ImageData.read_data\n调用load_image函数\n根据文件类型选择合适的VTK Reader\n如vtkNIFTIImageReader/vtkXMLImageDataReader等"]
    D3 --> D32["*******.2 SurfaceData.read_data\n调用load_surface函数\n根据文件类型选择合适的VTK Reader\n如vtkXMLPolyDataReader/vtkSTLReader等"]
    D --> D4["4.2 DataStorage.add_node\n将数据节点添加到存储中\n分配唯一ID并触发更新"]
    D4 --> D5["4.2.1 PipelineManager.update\n更新数据处理管线,这里pipeline._name是state='data_storage'"]
    D --> D6["4.3 render_window_manager.request_update_all\n请求所有渲染窗口更新"]
    D6 --> D7["4.3.1 RenderWindow.update\n更新渲染窗口\n调用vtk_render_window.Render()执行渲染"]
    D7 --> D71["******* 遍历数据存储中的所有节点\n根据视图类型(2D/3D)获取或创建合适的Mapper"]
    D71 --> D72["*******.1 MapperManager.generate_data_for_renderer\n为每个数据节点创建或获取合适的Mapper\n生成渲染所需的VTK对象"]
    D7 --> D73["******* 对于3D视图\n使用Mapper_3D类型的Mapper\n生成3D渲染数据"]
    D7 --> D74["******* 对于2D视图\n使用Mapper_2D类型的Mapper\n设置重切面矩阵\n生成2D渲染数据"]
    D7 --> D75["******* 将生成的Prop添加到Renderer\n执行vtk_render_window.Render()完成渲染"]
    D --> D8["4.4 ctrl.reset_camera\n重置相机视角以适应新加载的数据"]
    D --> D9["4.5 ctrl.view_update\n触发视图更新回调"]

    C --> J["3.1 RenderWindow.__init__\n初始化renderer，renderWindow，picker"]
    C --> K["3.2 renderWindow.setup\n初始化interactor，interactorStyle"]

    K --> L["3.2.1 renderWindow.update\n第一次执行时，data_storage的nodes为空，"]
    C --> M["3.3 ui/engine.py:initialize_binding\n注册state change callback\n负责用户界面相关的状态变化绑定"]

    M --> N["3.3.1 core/engine.py:initialize_internal_binding\n注册state change callback\n负责核心数据模型的状态变化绑定"]

    C --> O["3.4 创建SinglePageWithDrawerLayout\n设置应用布局结构"]
    O --> P["3.4.1 设置toolbar\n添加文件输入、视图切换、拾取模式等控件"]
    O --> Q["3.4.2 设置drawer\n添加DataNodesTree和DataPropertyCard组件"]
    O --> R["3.4.3 设置content\n创建渲染容器并添加VtkRemoteLocalView"]

    R --> S["3.4.3.1 为每个渲染窗口创建VtkRemoteLocalView\n将VTK渲染窗口与VtkRemoteLocalView连接"]
    S --> T["3.4.3.2 绑定控制器事件\n如on_server_ready、view_update、reset_camera等，特别的view_update的回调是手动触发的，例如：ctl.view_update()"]
    T --> U["3.4.3.3 设置视图更新回调\n确保状态变化时视图得到更新，例如：html_view.update会调用render_window.render()"]
    M --> V["3.3.2 注册文件上传回调state('files')、视图布局回调viewLayout、拾取模式回调pickingMode、坐标轴显示回调show_axes_widget，以及其他"]
```
