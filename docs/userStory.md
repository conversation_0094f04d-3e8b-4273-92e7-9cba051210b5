## AppBase与Workbench关系

```mermaid
graph TD
    A[AppBase] --父类--> B["Workbench(examples下的Workbench)"]
```

## 核心数据模型状态变化调用链

initialize_internal_binding (核心层):

* 处理数据属性变化: 如  surface_color,  depth_peeling,  current_representation
* 更新数据节点属性: 如  color,  representation,  scalar_opacity
* 请求渲染更新: render_window_manager.request_update_all()
* 不涉及文件加载、用户交互等UI操作

### surface_color、current_representation、image_level_window、pointsize

```mermaid
graph TD
    A["@state.change('surface_color')"\n循环dataStorage.nodes，修改node属性] --> B[render_window_manager.request_update_all\n循环每个renderWindow，调用update]
    B --> C[RenderWindow.update]
    C --> D[Mapper.generate_data_for_renderer，通过localStorage，设置node的visible属性，node数据更新到vtpMapper上]
    D --> E[Mapper.get_prop，有的返回actor，有的返回volume]
    E --> F[Mapper._apply_actor_properties，通过localStorage，将node其他属性更新到vtkActor上]
    F --> G[RenderWindow.render_window.Render]
```

### depth_peeling

```mermaid
graph TD
    A["@state.change('depth_peeling')"\n找到3D类型的renderWindow，设置renderer.depth_peeling] --> B[render_window_manager.request_update_all\n循环每个renderWindow，调用update]
    B --> C[RenderWindow.update]
    C --> D[Mapper.generate_data_for_renderer，通过localStorage，设置node的visible属性，node数据更新到vtpMapper上]
    D --> E[Mapper.get_prop，有的返回actor，有的返回volume]
    E --> F[Mapper._apply_actor_properties，通过localStorage，将node其他属性更新到vtkActor上]
    F --> G[RenderWindow.render_window.Render]
```

## 用户界面状态变化调用链

initialize_binding (UI层):

* 处理文件上传: @state.change("files")
* 处理文件对话框: @ctrl.set("load_data")
* 处理视图布局: @state.change("viewLayout")
* 处理拾取模式: @state.change("pickingMode")
* 处理点击数据: @state.change("pickData")
* 处理UI组件状态: 如 show_axes_widget

### 文件上传 (@state.change("files"))

files状态改变是通过layout.toolbar中的vuetify.VFileInput控件上传文件触发的。

```mermaid
graph TD
    A["@state.change('files')"\n检查上传的文件列表] --> B[load_client_files\n处理客户端上传的文件]
    B --> C[import_image_file\n根据文件类型调用相应的导入函数]
    C --> D[DataStorage.add_node\n将导入的数据添加到数据存储中]
    D --> E[PipelineManager.update\n更新数据管线]
    E --> F[render_window_manager.request_update_all\n请求所有渲染窗口更新]
    F --> G[RenderWindow.update\n更新渲染窗口]
    G --> H[Mapper.generate_data_for_renderer\n生成渲染数据]
    H --> I[RenderWindow.render_window.Render\n执行渲染]
    B --> J[ctrl.reset_camera\n重置相机视角]
```

### 视图布局 (@state.change("viewLayout"))

```mermaid
graph TD
    A["@state.change('viewLayout')"\n检测布局变化] --> B[workbench.update_layout\n更新工作台布局]
    B --> C[render_window_manager.update_layout\n更新渲染窗口管理器的布局]
    C --> D[render_window_manager.request_update_all\n请求所有渲染窗口更新]
    D --> E[RenderWindow.update\n更新渲染窗口]
    E --> F[Mapper.generate_data_for_renderer\n生成渲染数据]
    F --> G[RenderWindow.render_window.Render\n执行渲染]
    B --> H[ctrl.view_update\n更新视图]
```

### 拾取模式 (@state.change("pickingMode"))

```mermaid
graph TD
    A["@state.change('pickingMode')"\n检测拾取模式变化] --> B[更新交互设置\ninteractorSettings]
    B --> C[更新提示信息\ntooltip和tooltipStyle]
    C --> D[更新指针可见性\npointerVisibility]
    D --> E[重置选择相关状态\nfrustrum, selection, selectData]
```

### 点击数据 (@state.change("pickData"))

```mermaid
graph TD
    A["@state.change('pickData')"\n检测点击数据变化] --> B[检查点击模式\nmode=='remote'或'local']
    B --> C[获取点击位置\nposition或worldPosition]
    C --> D[遍历data_storage.nodes\n查找PointSet类型节点]
    D --> E[render_window.pick\n获取世界坐标]
    E --> F[pointset_data.set_point或add_point\n更新或添加点]
    F --> G[state.points_info = pointset_data.to_list\n更新状态中的点信息]
    G --> H[render_window_manager.request_update_all\n请求所有渲染窗口更新]
    H --> I[ctrl.view_update\n更新视图]
```

### 坐标轴显示 (@state.change("show_axes_widget"))

```mermaid
graph TD
    A["@state.change('show_axes_widget')"\n检测坐标轴显示状态变化] --> B[遍历render_window_manager.render_windows\n获取所有渲染窗口]
    B --> C[render_window.set_axes_visibility\n设置坐标轴可见性]
    C --> D[render_window_manager.request_update_all\n请求所有渲染窗口更新]
    D --> E[RenderWindow.update\n更新渲染窗口]
    E --> F[RenderWindow.render_window.Render\n执行渲染]
```

## 总结

1. 多窗口下，所有窗口共用一个dataStorage对象。
2. render_window_manager是单例的，管理所有renderWindow
3. mapper_manager也是单例的，管理所有mapper，根据node.id和mapper_type获取mapper
4. SinglePageWithDrawerLayout as layout，layout.toolbar是顶部工具栏；layout.drawer是侧边栏；layout.content是内容区域。

## 疑问

1. Workbench.state.update是干嘛的？
2. Workbench.colors哪里用到了？
3. Trame中调用了render_window.Render()，为什么还需要rendeerserver.controller.view_update？
4. 什么情况下会更改files这个state？代码在哪里？



