[metadata]
name = usml
author = Union Strong Algorithm Group
url = http://************:8443/songling/USML
description = AI Toolkit for Healthcare Imaging
long_description = file:README.md
long_description_content_type = text/markdown; charset=UTF-8
platforms = OS Independent
license = Apache License 2.0
license_files =
    LICENSE
project_urls =
    Bug Tracker=http://************:8443/songling/USML/issues
    Source Code=http://************:8443/songling/USML

[options]
python_requires = >= 3.7
# for compiling and develop setup only
# no need to specify the versions so that we could
# compile for multiple targeted versions.
setup_requires =
    ninja
install_requires =
    numpy>=1.17

[options.extras_require]
all =
	monai==1.0.0
	pyg==2.1.0


[flake8]
select = B,C,E,F,N,P,T4,W,B9
max_line_length = 120
# C408 ignored because we like the dict keyword argument syntax
# E501 is not flexible enough, we're using B950 instead
ignore =
    E203
    E501
    E741
    W503
    W504
    C408
    N812  
    B023 
per_file_ignores = __init__.py: F401, __main__.py: F401
exclude = *.pyi,.git,.eggs,usml/_version.py,versioneer.py,venv,.venv,_version.py

[isort]
known_first_party = usml
profile = black
line_length = 120
skip = .git, .eggs, venv, .venv, versioneer.py, _version.py, conf.py, usml/__init__.py
skip_glob = *.pyi

[versioneer]
VCS = git
style = pep440
versionfile_source = usml/_version.py
versionfile_build = usml/_version.py
tag_prefix =
parentdir_prefix =

[mypy]
# Suppresses error messages about imports that cannot be resolved.
ignore_missing_imports = True
# Changes the treatment of arguments with a default value of None by not implicitly making their type Optional.
no_implicit_optional = True
# Warns about casting an expression to its inferred type.
warn_redundant_casts = True
# No error on unneeded # type: ignore comments.
warn_unused_ignores = False
# Shows a warning when returning a value with type Any from a function declared with a non-Any return type.
warn_return_any = True
# Prohibit equality checks, identity checks, and container checks between non-overlapping types.
strict_equality = True
# Shows column numbers in error messages.
show_column_numbers = True
# Shows error codes in error messages.
show_error_codes = True
# Use visually nicer output in error messages: use soft word wrap, show source code snippets, and show error location markers.
pretty = False

[mypy-versioneer]
# Ignores all non-fatal errors.
ignore_errors = True

[mypy-usml._version]
# Ignores all non-fatal errors.
ignore_errors = True

[mypy-usml.eggs]
# Ignores all non-fatal errors.
ignore_errors = True

[pytype]
# Space-separated list of files or directories to exclude.
exclude = versioneer.py _version.py
# Space-separated list of files or directories to process.
inputs = usml
# Keep going past errors to analyze as many files as possible.
keep_going = True
# Run N jobs in parallel.
jobs = 8
# All pytype output goes here.
output = .pytype
# Paths to source code directories, separated by ':'.
pythonpath = .
# Check attribute values against their annotations.
check_attribute_types = True
# Check container mutations against their annotations.
check_container_types = True
# Check parameter defaults and assignments against their annotations.
check_parameter_types = True
# Check variable values against their annotations.
check_variable_types = True
# Comma or space separated list of error names to ignore.
disable = pyi-error
# Report errors.
report_errors = True
# Experimental: Infer precise return types even for invalid function calls.
precise_return = True
# Experimental: solve unknown types to label with structural types.
protocols = True
# Experimental: Only load submodules that are explicitly imported.
strict_import = False

[coverage:run]
concurrency = multiprocessing
source = .
data_file = .coverage/.coverage
omit = setup.py

[coverage:report]
exclude_lines =
    pragma: no cover
    if TYPE_CHECKING:
    # Don't complain if tests don't hit code:
    raise NotImplementedError
    if __name__ == .__main__.:
show_missing = True
skip_covered = True

[coverage:xml]
output = coverage.xml
